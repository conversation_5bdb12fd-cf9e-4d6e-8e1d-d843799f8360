package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/server"
)

func main() {
	// Create server instance
	srv := server.New(config.Port)

	// Create context that can be cancelled
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		if err := srv.Start(ctx); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()

	// Wait for shutdown signal
	<-sigChan
	fmt.Println("Received shutdown signal, stopping server...")

	// Cancel context to trigger graceful shutdown
	cancel()

	fmt.Println("Server shutdown complete")
}
