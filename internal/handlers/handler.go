package handlers

import (
	"log"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/request"
	"github.com/nickabs/http/internal/response"
)

type Handler func(w response.Writer, req *request.Request)

func RootHandler(w response.Writer, req *request.Request) {
	w.Response.Headers = response.GetDefaultHeaders(0)
	err := w.WriteStatusLine(config.StatusOK)
	if err != nil {
		log.Printf("Debug error returned, %v", err)
		w.WriteError(response.ResponseError{StatusCode: config.StatusServerError, StatusMsg: "server error: could not write status"})
	}

	err = w.WriteHeaders()
	if err != nil {
		log.Printf("Debug error returned from WriteHeaders, %v", err)
		w.WriteError(response.ResponseError{StatusCode: config.StatusServerError, StatusMsg: "server error: could not write headers"})
	}

}
