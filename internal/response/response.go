package response

import (
	"fmt"
	"io"
	"log"
	"strconv"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/headers"
)

type Response struct {
	StatusLine string
	Headers    headers.Headers
	Body       []byte
}

type ResponseError struct {
	StatusCode int
	StatusMsg  string
}

func NewResponse() Response {
	return Response{
		Headers: headers.NewHeaders(),
		Body:    make([]byte, 0),
	}
}

type Writer struct {
	Writer   io.Writer
	Response Response
}

func NewWriter(w io.Writer) Writer {
	return Writer{
		Writer:   w,
		Response: NewResponse(),
	}
}

func (w *Writer) Headers() headers.Headers {
	return w.Response.Headers
}

func (w *Writer) Write(data []byte) (int, error) {
	n, err := w.Writer.Write(data)
	return n, err
}

func (w *Writer) WriteError(re ResponseError) {
	errMsg := fmt.Sprintf("HTT/1.1 %v %v", re.StatusCode, re.StatusMsg)
	log.Print(errMsg)
	w.Write([]byte(GetDefaultHeaders(0)))
	w.Write([]byte(errMsg))

}
func (w *Writer) WriteHeaders() error {

	for k, v := range w.Response.Headers {
		_, err := w.Write([]byte(fmt.Sprintf("%v:%v\r\n", k, v)))
		if err != nil {
			return err
		}
	}

	// close headers
	_, err := w.Write([]byte("\r\n"))
	return err
}

func (w *Writer) WriteStatusLine(statusCode int) error {

	switch statusCode {
	case config.StatusOK:
		w.Response.StatusLine = "HTTP/1.1 200 OK"
	case config.StatusBadRequest:
		w.Response.StatusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		w.Response.StatusLine = "HTTP/1.1 400 Server Error"
	default:
		return fmt.Errorf("invalid status code: %v", statusCode)
	}

	_, err := w.Write([]byte(w.Response.StatusLine + "\r\n"))

	return err
}

func GetDefaultHeaders(contentLen int) headers.Headers {

	headers := headers.NewHeaders()
	headers.Set("Content-length", strconv.Itoa(contentLen))
	headers.Set("Connection", "close")
	headers.Set("Content-Type", "text/plain")
	return headers
}
