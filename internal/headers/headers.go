package headers

import (
	"fmt"
	"strings"
)

type Headers map[string]string

func NewHeaders() Headers {
	return map[string]string{}
}

func (h Headers) Set(key, value string) {

	// convert key to lowercase
	key = strings.ToLower(key)

	// names can be repeated (values should be concatenated separarated by commas)
	v, ok := h[key]
	if ok {
		h[key] = fmt.Sprintf("%s, %s", v, value)
	} else {
		h[key] = value
	}

}

func (h Headers) Get(key string) string {
	return h[strings.ToLower(key)]
}

func (h Headers) String() string {
	s := ""
	for k, v := range h {
		s += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	return s
}
