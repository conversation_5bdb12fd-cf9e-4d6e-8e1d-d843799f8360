package request

import (
	"fmt"
	"io"
	"strconv"
	"testing"

	"github.com/nickabs/http/internal/config"
)

func TestRequest(t *testing.T) {

	// Note: Server startup removed to avoid import cycle
	// If you need a test server, consider creating a test-specific server function

	tests := []struct {
		name            string
		request         string
		serverAddr      string
		wantBody        string
		wantRequestLine RequestLine
		wantHeaders     int
		expectError     bool
	}{
		{
			name:     "valid server w. headers and body",
			request:  "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n{}",
			wantBody: "{}",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  config.ServerAddr,
			expectError: false,
		},
		{
			request:  "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n{}",
			wantBody: "{}",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  config.ServerAddr,
			expectError: false,
		},

		{
			name:    "valid server",
			request: "POST /coffee HTTP/1.1\r\n",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 0,
			serverAddr:  config.ServerAddr,
			expectError: false,
		},
		{
			name:    "valid server w. headers",
			request: "POST /coffee HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n",
			wantRequestLine: RequestLine{
				HttpVersion:   "HTTP/1.1",
				RequestTarget: "/coffee",
				Method:        "POST",
			},
			wantHeaders: 3,
			serverAddr:  config.ServerAddr,
			expectError: false,
		},

		{
			name:        "bad method",
			request:     "123 /coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "missing method",
			request:     "/coffee HTTP/1.1\r\n",
			expectError: true,
		},
		{
			name:        "bad version ",
			request:     "POST /coffee HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "out of order ",
			request:     "/coffee POST HTTP/1.2\r\n",
			expectError: true,
		},
		{
			name:        "bad name",
			request:     "POST /coffee blah/1.2\r\n",
			expectError: true,
		},
		{
			name:        "empty request",
			request:     "",
			serverAddr:  config.ServerAddr,
			expectError: true,
		},
		{
			name:        "no target response",
			request:     "POST  HTTP/1.1\r\n",
			serverAddr:  config.ServerAddr,
			expectError: true,
		},
		{
			name:        "no crlf termination",
			request:     "POST /coffee HTTP/1.1",
			expectError: true,
		},
	}

	for _, tt := range tests {

		t.Run(tt.name, func(t *testing.T) {

			r := NewRequest()
			//testReader := strings.NewReader((tt.request))

			testReader := &chunkReader{
				data:            tt.request,
				numBytesPerRead: 3,
				pos:             0,
			}

			err := r.ReadRequest(testReader)
			if tt.expectError && err != nil {
				t.Log(err)
				return
			}

			if tt.expectError && err == nil {
				t.Errorf("expected an error but got success")
				return
			}

			if !tt.expectError && err != nil {
				t.Errorf("did not expect error but got %v", err)
				return
			}

			if len(r.Headers) != tt.wantHeaders {
				t.Errorf("wanted %v headers, got %v\n", tt.wantHeaders, len(r.Headers))
			}

			if r.RequestLine.HttpVersion != tt.wantRequestLine.HttpVersion {
				t.Errorf("expected httpVersion %v got %v ", tt.wantRequestLine.HttpVersion, r.RequestLine.HttpVersion)
			}
			if r.RequestLine.RequestTarget != tt.wantRequestLine.RequestTarget {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequestLine.RequestTarget, r.RequestLine.RequestTarget)
			}
			if r.RequestLine.Method != tt.wantRequestLine.Method {
				t.Errorf("expected requestTarget %v got %v ", tt.wantRequestLine.Method, r.RequestLine.Method)
			}

			if tt.wantBody != "" && string(r.Body) != tt.wantBody {
				t.Errorf("expected %v body and got %v", tt.wantBody, r.Body)
			}

		})
	}
}

func TestBody(t *testing.T) {

	body := []string{
		"hello world\n",
		"hello world\r\n",
		"",
		`{ "key": "value" }`,
		"\r\n",
	}
	for _, wantBody := range body {

		reader := &chunkReader{
			data: "POST /submit HTTP/1.1\r\n" +
				"Host: localhost:42069\r\n" +
				"Content-Length: " + strconv.Itoa(len(wantBody)) + "\r\n" +
				"\r\n" +
				wantBody,
			numBytesPerRead: 3,
			pos:             0,
		}

		req := NewRequest()

		err := req.ReadRequest(reader)
		if err != nil {
			t.Fatal(err)
		}

		t.Logf("body |%v|", string(req.Body))

		if string(req.Body) != wantBody {
			t.Errorf("expected body |%v| got |%v|", wantBody, req.Body)
		}
	}
}
func TestHeaders(t *testing.T) {

	tests := []struct {
		name        string
		header      string
		wantName    string
		wantValue   string
		expectError bool
	}{
		{
			name:        "header w. valid whitespace",
			header:      "Accept:     */*\r\n",
			wantName:    "accept",
			wantValue:   "*/*",
			expectError: false,
		},
		{
			name:        "header w. invalid name",
			header:      "A@cept:     */*\r\n",
			expectError: true,
		},
		{
			name:        "header w. invalid whitespace",
			header:      "Accept :     */*\r\n",
			expectError: true,
		},
		{
			name:        "header w. invalid header ",
			header:      "Accept     */*\r\n",
			expectError: true,
		},
		{
			name:        "header w. empty value ",
			header:      "Accept:\r\n",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			r := NewRequest()

			_, _, err := r.ParseHeader([]byte(tt.header))
			if err != nil && tt.expectError {
				t.Logf("error: %v\n", err)
				return
			}

			if tt.expectError && err == nil {
				t.Errorf("expected errror and got sucess")
				return

			}
			if !tt.expectError && err != nil {
				t.Errorf("did not expect error and got %v", err)
				return
			}

			if r.Headers[tt.wantName] != tt.wantValue {
				t.Errorf("expected value: %v, got %v", tt.wantValue, r.Headers[tt.wantName])
				return
			}
		})
	}

}

func TestRepeatedFieldValues(t *testing.T) {

	r := NewRequest()

	wantValue := "value-0, value-1, value-2"
	for i := range 3 {

		header := fmt.Sprintf("multi: value-%v\r\n", i)

		_, _, err := r.ParseHeader([]byte(header))
		if err != nil {
			t.Errorf("could not parse header")
			return
		}
	}
	if len(r.Headers) != 1 {
		t.Errorf("expected 1 header got %v", len(r.Headers))
		return
	}
	if r.Headers["multi"] != wantValue {
		t.Errorf("expected value of %v got %v", wantValue, r.Headers["multi"])
	}
}

type chunkReader struct {
	data            string
	numBytesPerRead int
	pos             int
}

func TestChunkReaderInput(t *testing.T) {
	t.Run("chunking test", func(t *testing.T) {

		req := NewRequest()

		reader := &chunkReader{
			data:            "GET / HTTP/1.1\r\nHost: localhost:42069\r\nUser-Agent: curl/7.81.0\r\nAccept: */*\r\n\r\n",
			numBytesPerRead: 3,
			pos:             0,
		}

		for i := 1; i <= len(reader.data); i++ {

			t.Logf("attempting reads of %v bytes", i)

			reader.numBytesPerRead = i
			reader.pos = 0
			err := req.ReadRequest(reader)
			if err != nil {
				t.Fatal(err)
			}

			if req.RequestLine.Method != "GET" {
				t.Errorf("got %v expected GET", req.RequestLine.Method)
			}

			if len(req.Headers) != 3 {
				t.Errorf("expected 3 headers got %v", len(req.Headers))
			}
		}
	})
}

// Read reads up to len(p) or numBytesPerRead bytes from the string per call
// its useful for simulating reading a variable number of bytes per chunk from a network connection
func (cr *chunkReader) Read(p []byte) (n int, err error) {
	if cr.pos >= len(cr.data) {
		return 0, io.EOF
	}
	endIndex := cr.pos + cr.numBytesPerRead
	if endIndex > len(cr.data) {
		endIndex = len(cr.data)
	}
	n = copy(p, cr.data[cr.pos:endIndex])
	cr.pos += n
	if n > cr.numBytesPerRead {
		n = cr.numBytesPerRead
		cr.pos -= n - cr.numBytesPerRead
	}
	return n, nil
}
