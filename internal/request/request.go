package request

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"slices"
	"strconv"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/headers"
)

type Request struct {
	RequestLine RequestLine
	Headers     headers.Headers
	Body        []byte
	State       int
}
type RequestLine struct {
	HttpVersion   string
	RequestTarget string
	Method        string
}

// parsing states
const (
	stateInitialized = 0
	stateHeaders     = 1
	stateBody        = 2
)

func NewRequest() Request {
	return Request{
		State:   stateInitialized,
		Headers: headers.NewHeaders(),
		Body:    make([]byte, 0),
	}
}

// read the request and set up the request struct
func (r *Request) ReadRequest(input io.Reader) error {

	buffer := make([]byte, config.BufferSize)
	var data []byte
	r.State = stateInitialized

	eof := false

	// read the input into buffer until eof or request is complete
	for !eof {
		// First, try to parse any remaining data in the buffer
		if len(data) > 0 {
			bytesParsed, err := r.Parse(data)
			if err != nil {
				return fmt.Errorf("error parsing data %v", err)
			}
			if bytesParsed > 0 {
				data = data[bytesParsed:]
				continue // Continue parsing if we made progress
			}
		}

		// Check if we're done reading
		if r.State == stateBody {
			// For GET requests, no body expected
			if r.RequestLine.Method == "GET" {
				break
			}

			// For POST requests with Content-Length, check if we have enough data
			contentLengthStr := r.Headers.Get("content-length")

			if contentLengthStr != "" {
				contentLength, err := strconv.Atoi(contentLengthStr)
				if err != nil {
					return fmt.Errorf("error: malformed content-length value: %v", contentLengthStr)
				}
				// If we have enough data for the body, we're done
				if len(data) >= contentLength {
					break
				}
			}
			// For POST requests without Content-Length, continue reading until EOF
		}

		// Only read more data if we need it
		n, err := input.Read(buffer)
		if err != nil {
			if errors.Is(err, io.EOF) {
				// continue this loop to parse any remaing data
				eof = true
			} else {
				return fmt.Errorf("error reading input: %w", err)
			}
		}

		// Append new data to accumulated buffer
		data = append(data, buffer[:n]...)
	}

	if r.State == stateInitialized {
		return fmt.Errorf("incomplete request - no request line found")
	}

	if r.State == stateBody {
		r.Body = data
		// check for content-size
		v, ok := r.Headers["content-length"]
		if ok {
			size, err := strconv.Atoi(v)
			if err != nil {
				return fmt.Errorf("error: malformed content-length value: %v", v)
			}

			if size != len(data) {
				return fmt.Errorf("error: data length of %v not equal to content-length %v", len(data), size)
			}
		}
	}

	return nil
}

// Parse checks the data for a crlf delimitor and
// incorporates the data preceeding the delimitor into the request, returning the number of bytes parsed
//
// The function returns 0 when the data is incomplete (no crlf deliminator found).
func (r *Request) Parse(data []byte) (int, error) {

	// loop through the data and process each section terminated by crlf
	switch r.State {
	case stateInitialized:
		bytesParsed, err := r.ParseRequestLine(data)
		if err != nil {
			return 0, err
		}
		if bytesParsed > 0 {
			r.State = stateHeaders
		}
		return bytesParsed, nil
	case stateHeaders:
		bytesParsed, done, err := r.ParseHeader(data)
		if err != nil {
			return 0, err
		}

		if done {
			r.State = stateBody
		}
		return bytesParsed, nil
	case stateBody:
		return 0, nil // keep processing to eof
	default:
		return 0, fmt.Errorf("error: unexpected state: %v", r.State)
	}

}

var tokenChars = []byte{'!', '#', '$', '%', '&', '\'', '*', '+', '-', '.', '^', '_', '`', '|', '~'}

func isTokenChar(c byte) bool {
	if c >= 'A' && c <= 'Z' ||
		c >= 'a' && c <= 'z' ||
		c >= '0' && c <= '9' {
		return true
	}

	return slices.Contains(tokenChars, c)
}

func isHttpValidToken(httpToken []byte) bool {
	for _, c := range httpToken {
		if !isTokenChar(c) {
			return false
		}
	}
	return true
}

// parse header and add valid lines to r.Headers[] - returns the number of bytes parsed
// sets r.State = body when the first empy line is encountered (ie. crlf immediately followed by crlf)
func (r *Request) ParseHeader(data []byte) (int, bool, error) {
	idx := bytes.Index(data, []byte("\r\n"))
	// no terminator found, get more data
	if idx == -1 {
		return 0, false, nil
	}

	// empty line signifies end of headers
	if idx == 0 {
		return 2, true, nil
	}

	line := data[:idx]

	headerIdx := bytes.Index(line, []byte(":"))

	if headerIdx == -1 {
		return 0, true, fmt.Errorf("error: header invalid format")
	}

	name := line[:headerIdx]

	// whitespace is not allowed around field-name
	if len(name) != len(bytes.TrimSpace(name)) {
		return 0, true, fmt.Errorf("error: header name contains whitespace")
	}

	//trim whitespace from field-value
	value := bytes.TrimSpace(line[headerIdx+1:])
	if len(value) == 0 {
		return 0, true, fmt.Errorf("error: empty value supplied in header")
	}

	// field names must be a valid http token
	if !isHttpValidToken(name) {
		return 0, true, fmt.Errorf("error: field-name %v is not a valid token", name)
	}

	r.Headers.Set(string(name), string(value))

	// num bytes parsed includes the crlf delim
	return len(line) + 2, false, nil

}

// ParseRequestLine parses the request line into method/target/http version, sets r.RequestLine and returns the number of bytes parsed
func (r *Request) ParseRequestLine(data []byte) (int, error) {

	idx := bytes.Index(data, []byte("\r\n"))
	// no terminator found, get more data
	if idx == -1 {
		return 0, nil
	}

	line := data[:idx]

	// Split on spaces using bytes
	parts := bytes.Split(line, []byte(" "))
	if len(parts) != 3 {
		return 0, fmt.Errorf("invalid request line format, expected 3 parts but got %d", len(parts))
	}

	method := parts[0]
	target := parts[1]
	version := parts[2]

	// Validate method (must be uppercase letters)
	if len(method) == 0 {
		return 0, fmt.Errorf("method cannot be empty")
	}
	for _, b := range method {
		if b < 'A' || b > 'Z' {
			return 0, fmt.Errorf("method must be all uppercase letters")
		}
	}

	httpPrefix := []byte("HTTP/")
	if !bytes.HasPrefix(bytes.ToUpper(version), httpPrefix) {
		return 0, fmt.Errorf("invalid HTTP version format: %s", version)
	}

	versionNum := version[5:]
	if !bytes.Equal(versionNum, []byte("1.1")) {
		return 0, fmt.Errorf("only HTTP/1.1 is supported, got: %s", version)
	}

	pos := bytes.Index(target, []byte("/"))
	if pos == -1 {
		return 0, fmt.Errorf("target must contain a /")
	}

	r.RequestLine.Method = string(method)
	r.RequestLine.RequestTarget = string(target)
	r.RequestLine.HttpVersion = string(version)

	return len(line) + 2, nil
}
