package listener

import (
	"fmt"
	"log"
	"net"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/request"
)

// todo remove
// create a tcp listener
// accept connections and print receieved data one line at a time
func Listen() {
	listener, err := net.Listen("tcp", config.ServerAddr)
	if err != nil {
		panic(fmt.Sprintf("could not create listener %v", err))
	}
	fmt.Printf("listening on %s \n", config.ServerAddr)

	defer listener.Close()

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Fatalf("could not get a connection %v", err)
			return
		}
		fmt.Printf("connection accepted from %s \n", conn.RemoteAddr().String())
		defer conn.Close()

		req := request.NewRequest()

		req.ReadRequest(conn)

		fmt.Printf("\nRequest:\n")
		fmt.Printf("Method : %v \n", req.RequestLine.Method)
		fmt.Printf("Target : %v \n", req.RequestLine.RequestTarget)
		fmt.Printf("Version : %v \n\n", req.RequestLine.HttpVersion)

		fmt.Printf("\nHeaders:\n")
		for h := range req.Headers {
			fmt.Printf("%v: %v \n", h, req.Headers[h])
		}

		fmt.Printf("\nBody:\n")
		fmt.Println(string(req.Body))

	}

}
